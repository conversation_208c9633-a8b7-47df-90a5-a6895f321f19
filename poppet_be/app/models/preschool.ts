import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class Preschool extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  declare description: string

  @column({ prepare: (value) => JSON.stringify(value) })
  declare region: string | Array<string>

  declare language: string

  @column({ consume: (value: boolean) => Boolean(value) })
  declare blocked: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @hasMany(() => Story)
  // declare stories: HasMany<typeof Story>
}
