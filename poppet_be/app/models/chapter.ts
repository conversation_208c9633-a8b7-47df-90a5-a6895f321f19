import { DateTime } from 'luxon'
import { BaseModel, column, computed } from '@adonisjs/lucid/orm'
import { generateUniqueSlug } from '../../migration-utils.js'

export type Option = {
  value: string
  threshold?: number
  chapterId?: number
  answer?: boolean
}

export default class Chapter extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  declare handle: string

  declare description: string

  declare storyId: number

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @belongsTo(() => Story)
  // declare story: BelongsTo<typeof Story>

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isFreeResponse: boolean

  declare audioUrl: string | null

  declare fileId: number

  // @belongsTo(() => File, {
  //   foreignKey: 'fileId',
  //   serializeAs: 'video',
  // })
  // declare video: BelongsTo<typeof File>

  @column({ prepare: (value) => JSON.stringify(value) })
  declare options: Array<Option> | string | null

  @column({ serializeAs: 'fallback_chapter_id' })
  declare fallbackChapterId: number | null

  // @belongsTo(() => Chapter, {
  //   foreignKey: 'fallbackChapterId',
  //   serializeAs: 'fallback_chapter',
  // declare fallbackChapter: BelongsTo<typeof Chapter>

  @computed({ serializeAs: 'is_last_video' })
  get isLastVideo() {
    return (this.options?.length ?? 0) <= 0
  }

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Manual slug generation to replace @slugify decorator
  static async generateHandle(title: string): Promise<string> {
    const baseSlug = generateUniqueSlug(title)

    // Check if slug exists and increment if needed
    let finalSlug = baseSlug
    let counter = 1

    while (await Chapter.findBy('handle', finalSlug)) {
      finalSlug = `${baseSlug}-${counter}`
      counter++
    }

    return finalSlug
  }
}
