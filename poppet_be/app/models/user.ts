import { DateTime } from 'luxon'
import hash from '@adonisjs/core/services/hash'
import {
  BaseModel,
  column,
  beforeSave,
  beforeCreate
} from '@adonisjs/lucid/orm'
import { DbAccessTokensProvider } from '@adonisjs/auth/access_tokens'

export const generateReferralCode = (length: number = 6) => {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 1; i <= length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

export default class User extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare stripeCustomerId: string

  @column()
  declare email: string

  @column()
  declare region: string

  @column({ serializeAs: null })
  declare password: string

  @column()
  declare referralCode: string

  @column()
  declare name: string

  @column()
  declare phone: string

  @column({ columnName: 'address_1', serializeAs: 'address_1' })
  declare address1: string

  @column({ columnName: 'address_2', serializeAs: 'address_2' })
  declare address2: string

  @column()
  declare postcode: string

  @column()
  declare rememberMeToken: string | null

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isAnonymous: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare verified?: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare phoneVerified?: boolean

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare blocked?: boolean

  @column()
  declare userGroupId: number | null

  @column()
  declare activeChildId: number

  @column.dateTime()
  declare promotionEndedAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime | null

  // Hooks
  @beforeSave()
  static async hashPassword(user: User) {
    if (user.$dirty.password) {
      user.password = await hash.make(user.password)
    }
  }

  @beforeCreate()
  static async generateReferral(user: User) {
    if (!user.$dirty.referralCode) {
      let repeat = false
      let referralCode: string
      do {
        referralCode = generateReferralCode(8)
        if (await User.findBy('referral_code', referralCode)) {
          repeat = true
        } else {
          repeat = false
        }
      } while (repeat || !referralCode)
      user.referralCode = referralCode
    }
  }

  // Static methods
  static async verifyPassword(oldPassword: string, hashedPassword: string) {
    const checkPassword = await hash.verify(hashedPassword, oldPassword)
    return checkPassword
  }

  static accessTokens = DbAccessTokensProvider.forModel(User)
}
