import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import PackFilter from '#models/filters/pack_filter'

export default class Pack extends compose(BaseModel, Filterable) {
  static $filter = () => PackFilter
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  declare description: string | null

  declare thumbnailUrl: string | null

  declare featuredImage: string

  @column({ prepare: (value) => JSON.stringify(value) })
  declare region: string | Array<string>

  declare language: string

  declare noOfLevel: number

  declare store_url: string

  declare slides: Array<any> | string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @hasMany(() => Story, {
  //   serializeAs: 'stories',
  // })
  // declare stories: HasMany<typeof Story>

  // @hasMany(() => PackLevel, {
  //   serializeAs: 'pack_levels',
  // declare packLevels: HasMany<typeof PackLevel>

  // @manyToMany(() => User, {
  //   pivotTable: 'user_packs',
  //   pivotColumns: ['current_level'],
  //   pivotForeignKey: 'pack_id',
  //   pivotRelatedForeignKey: 'user_id',
  //   pivotTimestamps: true,
  //   serializeAs: 'users',
  // declare userPacks: ManyToMany<typeof User>

  // @hasMany(() => PackCode, {
  //   serializeAs: 'pack_codes',
  // declare packCodes: HasMany<typeof PackCode>
}
