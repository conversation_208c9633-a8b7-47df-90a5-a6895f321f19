import { DateTime } from 'luxon'
import { BaseModel, column, computed } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import StoryFilter from '#models/filters/story_filter'
import { generateUniqueSlug } from '../../migration-utils.js'

export enum StoryType {
  COMMUNITY = 'community',
  GAME = 'game',
  PRESCHOOL = 'preschool',
  PACK = 'pack',
}

export default class Story extends compose(BaseModel, Filterable) {
  static $filter = () => StoryFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  declare handle: string

  declare description: string | null

  declare language: string

  // difficulty
  declare level: number

  @column({ prepare: (value) => JSON.stringify(value) })
  declare region: string | Array<string>

  declare thumbnailUrl: string | null

  declare previewImageUrl: string | null

  declare previewVideoId: number | null

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @belongsTo(() => File, { foreignKey: 'previewVideoId', serializeAs: 'preview_video' })
  // declare previewVideo: BelongsTo<typeof File>

  // break stories don't have options
  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isBreak: boolean

  declare qrCode: string

  @column({ consume: (value: number) => Number(value) })
  declare price: number

  declare compareAtPrice: number

  declare wordCount: number

  declare type: StoryType

  declare isCommunity: boolean

  declare communityReviewText: string

  declare isFeatured: boolean

  declare isExclusive: boolean

  declare status: string

  declare packId: number

  declare preschoolId: number | null

  declare ordering: number

  declare defaultChapterId: number | null

  declare metadata: any

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // @manyToMany(() => Plan, {
  //   pivotTable: 'plan_stories',
  //   pivotForeignKey: 'story_id',
  //   pivotRelatedForeignKey: 'plan_id',
  // })
  // declare plans: ManyToMany<typeof Plan>

  // @belongsTo(() => Pack)
  // declare pack: BelongsTo<typeof Pack>

  // @belongsTo(() => Preschool)
  // declare preschool: BelongsTo<typeof Preschool>

  // @hasMany(() => BundleStory)
  // declare bundleStory: HasMany<typeof BundleStory>

  // @hasOne(() => Chapter)
  // declare defaultChapter: HasOne<typeof Chapter>

  // @hasMany(() => PurchaseClickTracker, { serializeAs: 'purchase_clicks' })
  // declare purchaseClickTrackers: HasMany<typeof PurchaseClickTracker>

  // @hasMany(() => Chapter)
  // declare chapters: HasMany<typeof Chapter>

  // @hasMany(() => StoryOrder, { serializeAs: 'story_orders' })
  // declare storyOrders: HasMany<typeof StoryOrder>

  // @manyToMany(() => Tag, {
  //   pivotTable: 'story_tags',
  //   pivotTimestamps: true
  // declare tags: ManyToMany<typeof Tag>

  // Computed properties
  @computed({ serializeAs: 'has_redeemed' })
  get hasRedeemed() {
    return this.$extras.redeemed > 0 ? true : false
  }

  @computed({ serializeAs: 'purchase_clicks' })
  get clicks() {
    return this.$extras.clicks
  }

  @computed({ serializeAs: 'plan_id' })
  get planId() {
    return this.$extras.pivot_plan_id
  }

  @computed({ serializeAs: 'plan_level' })
  get planLevel() {
    return this.$extras.pivot_level
  }

  @computed({ serializeAs: 'plan_featured' })
  get planFeatured() {
    return this.$extras.pivot_is_featured
  }

  @computed({ serializeAs: 'plan_free' })
  get planFree() {
    return Boolean(this.$extras.pivot_is_free)
  }

  // Manual slug generation to replace @slugify decorator
  static async generateHandle(title: string): Promise<string> {
    const baseSlug = generateUniqueSlug(title)

    // Check if slug exists and increment if needed
    let finalSlug = baseSlug
    let counter = 1

    while (await Story.findBy('handle', finalSlug)) {
      finalSlug = `${baseSlug}-${counter}`
      counter++
    }

    return finalSlug
  }
}
